import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gather_point/core/managers/app_initialization_cubit/app_initialization_cubit.dart';
import 'package:gather_point/core/routing/routes_keys.dart';
import 'package:gather_point/core/services/service_locator.dart';
import 'package:gather_point/core/styles/app_text_styles.dart';
import 'package:gather_point/core/styles/theme_helper.dart';
import 'package:gather_point/core/utils/app_assets.dart';
import 'package:gather_point/core/utils/auth_utils.dart';
import 'package:gather_point/core/widgets/enhanced_page_layouts.dart';
import 'package:gather_point/core/widgets/enhanced_ui_components.dart';
import 'package:gather_point/core/widgets/enhanced_layouts.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/host/presentation/views/create_property_wizard.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_cubit.dart';
import 'package:gather_point/feature/profile/presentation/cubit/profile_state.dart';
import 'package:gather_point/feature/host/presentation/cubit/host_dashboard_cubit.dart';
import 'package:gather_point/feature/profile/presentation/views/previous_trips_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/notifications_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/friends_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/hosting_resources_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/co_host_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/early_access_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/support_center_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/legal_information_screen.dart';
import 'package:gather_point/feature/profile/presentation/views/enhanced_account_info_screen.dart';
import 'package:gather_point/generated/l10n.dart';
import 'package:go_router/go_router.dart';

class ProfileTabView extends StatelessWidget {
  const ProfileTabView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ProfileCubit>()..loadLocalUser(),
      child: const _ProfileTabViewContent(),
    );
  }
}

class _ProfileTabViewContent extends StatefulWidget {
  const _ProfileTabViewContent({super.key});

  @override
  _ProfileTabViewContentState createState() => _ProfileTabViewContentState();
}

class _ProfileTabViewContentState extends State<_ProfileTabViewContent> {
  @override
  void initState() {
    super.initState();
    // Load user data from local storage first
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cubit = context.read<ProfileCubit>();
      // Only load local user data if no state is available
      if (cubit.state is ProfileInitial) {
        cubit.loadLocalUser();
      }
    });
  }

  String _getGenderString(int genderCode, S s) {
    switch (genderCode) {
      case 1:
        return s.male;
      case 2:
        return s.female;
      default:
        return s.notSpecified;
    }
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final cubit = context.read<ProfileCubit>();
    debugPrint('🔄 ProfileView: Build with ProfileCubit instance: ${cubit.hashCode}');

    return MultiBlocListener(
      listeners: [
        BlocListener<ProfileCubit, ProfileState>(
          listener: (context, state) {
            debugPrint('🔄 ProfileView: ProfileCubit state changed to: ${state.runtimeType}');
            if (state is ProfileError) {
              debugPrint('❌ ProfileView: ProfileError received: ${state.message}');
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            } else if (state is ProfileLoading) {
              debugPrint('🔄 ProfileView: ProfileLoading state received');
            } else if (state is ProfileHosterModeToggled) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.user.isHosterMode
                      ? s.hostModeActivated
                      : s.hostModeDeactivated),
                  backgroundColor: Colors.green,
                ),
              );
            }
          },
        ),
        BlocListener<AppInitializationCubit, AppInitializationState>(
          listener: (context, state) {
            if (state is AppInitializationNavigateToHome) {
              // Close any open dialogs first
              Navigator.of(context, rootNavigator: true)
                  .popUntil((route) => route.isFirst);
              // Navigate to home and reload profile with new guest user
              GoRouter.of(context).go(RoutesKeys.kHomeViewTab);
              // Reload the profile with the new user data
              context.read<ProfileCubit>().loadLocalUser();
            }
          },
        ),
      ],
      child: BlocConsumer<ProfileCubit, ProfileState>(
        listener: (context, state) {
          // Additional profile-specific listeners can go here if needed
        },
        builder: (context, state) {
          debugPrint('🔄 ProfileView: BlocConsumer builder called with state: ${state.runtimeType}');
          UserEntity? user;
          bool isLoading = false;

          if (state is ProfileLoaded ||
              state is ProfileUpdated ||
              state is ProfileHosterModeToggled) {
            if (state is ProfileLoaded) user = state.user;
            if (state is ProfileUpdated) user = state.user;
            if (state is ProfileHosterModeToggled) user = state.user;
            debugPrint('✅ ProfileView: User loaded: ${user?.fullName ?? 'Unknown'}');
          } else if (state is ProfileLoading) {
            isLoading = true;
            debugPrint('🔄 ProfileView: Profile loading state');
          } else if (state is ProfileInitial) {
            debugPrint('🔄 ProfileView: Profile initial state - no user data');
            // For initial state, we should try to load local user or show guest state
            WidgetsBinding.instance.addPostFrameCallback((_) {
              context.read<ProfileCubit>().loadLocalUser();
            });
            isLoading = true; // Show loading while we load local user
          } else if (state is ProfileLoggedOut) {
            debugPrint('✅ ProfileView: Profile logged out state - showing post-logout dialog');
            // Handle logged out state by showing the post-logout dialog
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _showPostLogoutDialog(context);
            });
            isLoading = true; // Show loading while dialog is being shown
          }

          // Show shimmer loading when loading
          if (isLoading) {
            return EnhancedPageLayout(
              title: s.profile,
              showBackButton: false,
              hasBottomNavigation: true,
              body: const _ProfileLoadingShimmer(),
            );
          }

          // If user is still null at this point, show loading
          if (user == null) {
            debugPrint('❌ ProfileView: User is null, showing loading shimmer');
            return EnhancedPageLayout(
              title: s.profile,
              showBackButton: false,
              hasBottomNavigation: true,
              body: const _ProfileLoadingShimmer(),
            );
          }

          // Show floating action button only for authenticated users (not guests)
          final shouldShowFAB = !user.isGuest;

          return EnhancedPageLayout(
            title: s.profile,
            showBackButton: false,
            hasBottomNavigation: true,
            actions: null,
            body: _ProfileBody(user: user, getGenderString: _getGenderString),
            floatingActionButton:
                shouldShowFAB ? _ModeSwitchButton(user: user) : null,
          );
        },
      ),
    );
  }

  void _showPostLogoutDialog(BuildContext context) {
    final s = S.of(context);
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Text(s.logoutSuccessful),
        content: Text(s.chooseWhatToDoNow),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Continue as guest - trigger app initialization to get guest token
              context.read<AppInitializationCubit>().resetAppState();
            },
            child: Text(s.continueAsGuest),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              GoRouter.of(context).go(RoutesKeys.kLoginView);
            },
            child: Text(s.login),
          ),
        ],
      ),
    );
  }
}

class _ProfileBody extends StatelessWidget {
  final UserEntity? user;
  final String Function(int, S) getGenderString;

  const _ProfileBody({
    required this.user,
    required this.getGenderString,
  });

  @override
  Widget build(BuildContext context) {
    // Only show login prompt if user is null (no user data at all)
    if (user == null) {
      return AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: EnhancedEmptyState(
          icon: Icons.person_off_rounded,
          title: S.of(context).mustLogin,
          subtitle: S.of(context).mustLoginDescription,
          actionText: S.of(context).login,
          onActionPressed: () {
            GoRouter.of(context).go(RoutesKeys.kLoginView);
          },
        ),
      );
    }

    // Check if user is in hoster mode to show different profile layouts
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 400),
      transitionBuilder: (Widget child, Animation<double> animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0.0, 0.1),
              end: Offset.zero,
            ).animate(CurvedAnimation(
              parent: animation,
              curve: Curves.easeOutCubic,
            )),
            child: child,
          ),
        );
      },
      child: user!.isHosterMode
          ? _HosterProfileView(
              key: const ValueKey('hoster'),
              user: user!,
              getGenderString: getGenderString,
            )
          : _ClientProfileView(
              key: const ValueKey('client'),
              user: user!,
              getGenderString: getGenderString,
            ),
    );
  }
}

class _ClientProfileView extends StatelessWidget {
  final UserEntity user;
  final String Function(int, S) getGenderString;

  const _ClientProfileView({
    super.key,
    required this.user,
    required this.getGenderString,
  });
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ProfileCubit>();
    final isGuest = cubit.state is ProfileLoaded
        ? (cubit.state as ProfileLoaded).user.isGuest
        : true;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Profile Header Card
          _ClientProfileHeader(user: user),
          const SizedBox(height: 16),

          // Quick Actions Section
          if (!isGuest) const _QuickActionsSection(),
          if (!isGuest) const SizedBox(height: 16),

          // Settings Section
          const _SettingsSection(),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}

class _HosterProfileView extends StatelessWidget {
  final UserEntity user;
  final String Function(int, S) getGenderString;

  const _HosterProfileView({
    super.key,
    required this.user,
    required this.getGenderString,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => HostDashboardCubit(getIt())..loadDashboardData(),
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Profile Header with Stats
            _HosterProfileHeader(user: user),
            const SizedBox(height: 24),

            // Menu Items
            const _HosterMenuItems(),

            const SizedBox(height: 100), // Extra padding for floating button
          ],
        ),
      ),
    );
  }
}

class _HosterProfileHeader extends StatelessWidget {
  final UserEntity user;

  const _HosterProfileHeader({required this.user});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Profile Image and Name
          Row(
            children: [
              // Profile Image
              CircleAvatar(
                radius: 30,
                backgroundImage: user.image.isNotEmpty
                    ? CachedNetworkImageProvider(user.image)
                    : null,
                child: user.image.isEmpty
                    ? Icon(Icons.person, size: 30, color: Colors.grey.shade400)
                    : null,
              ),
              const SizedBox(width: 16),

              // Name and notification icon
              Expanded(
                child: Text(
                  user.fullName,
                  style: AppTextStyles.font18Bold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ),

              // Notification icon
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const NotificationsScreen(),
                    ),
                  );
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Stack(
                    children: [
                      Icon(
                        Icons.notifications_outlined,
                        color: context.primaryTextColor,
                        size: 24,
                      ),
                      // Notification badge (optional - shows when there are unread notifications)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: context.accentColor,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Stats Cards with Real Data
          BlocBuilder<HostDashboardCubit, HostDashboardState>(
            builder: (context, state) {
              if (state is HostDashboardLoaded) {
                final financialData = state.dashboardData.financialData;
                final statistics = state.dashboardData.statistics;

                return Row(
                  children: [
                    // Earnings Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.earnings,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${S.of(context).priceWithCurrency(financialData.thisMonthEarnings.toStringAsFixed(0))} خلال هذا الشهر',
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Reviews Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.insights,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(
                                  Icons.star,
                                  color: Colors.amber,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '${statistics.averageRating.toStringAsFixed(1)} • تقييم ${statistics.totalReviews}',
                                  style: AppTextStyles.font12Regular.copyWith(
                                    color: context.secondaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              } else if (state is HostDashboardLoading) {
                return Row(
                  children: [
                    // Loading shimmer for earnings
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.earnings,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 16,
                              width: 80,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Loading shimmer for reviews
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.insights,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Container(
                              height: 16,
                              width: 100,
                              decoration: BoxDecoration(
                                color: Colors.grey.shade300,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Error or initial state - show default values
                return Row(
                  children: [
                    // Earnings Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.earnings,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '${S.of(context).priceWithCurrency('0')} خلال هذا الشهر',
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    // Reviews Card
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              s.insights,
                              style: AppTextStyles.font14Regular.copyWith(
                                color: context.primaryTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                const Icon(
                                  Icons.star,
                                  color: Colors.amber,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  '0.0 • تقييم 0',
                                  style: AppTextStyles.font12Regular.copyWith(
                                    color: context.secondaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ],
      ),
    );
  }
}

class _HosterMenuItems extends StatelessWidget {
  const _HosterMenuItems();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: context.cardColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          _HosterMenuItem(
            icon: Icons.flash_on,
            title: s.earlyAccessFeatures,
            subtitle: s.newLabel,
            hasNew: true,
            onTap: () {
              // Navigate to early access features
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EarlyAccessScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            icon: Icons.settings,
            title: s.accountSettingsTitle,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EnhancedAccountInfoScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            icon: Icons.receipt_long,
            title: 'موارد الاستضافة',
            onTap: () {
              // Navigate to hosting resources
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const HostingResourcesScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            icon: Icons.help_outline,
            title: 'اطلب المساعدة',
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SupportCenterScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            image: AppAssets.iconsShare,
            title: 'العثور على مضيف مشارك',
            onTap: () {
              // Navigate to find co-host
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CoHostScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            icon: Icons.add_home,
            title: 'إنشاء إعلان جديد',
            onTap: () async {
              // Navigate to create new listing
              final result = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CreatePropertyWizard(),
                ),
              );

              // If property was created successfully, refresh dashboard data
              if (result == true && context.mounted) {
                // Refresh the host dashboard data to reflect new property
                context.read<HostDashboardCubit>().refreshDashboard();

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(s.propertyCreatedSuccessfully),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
          ),
          _HosterMenuItem(
            icon: Icons.business,
            title: 'إحالة مضيف',
            onTap: () {
              context.push('/refer-host');
            },
          ),
          _HosterMenuItem(
            icon: Icons.description,
            title: s.legal,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LegalInformationScreen(),
                ),
              );
            },
          ),
          _HosterMenuItem(
            icon: Icons.logout,
            title: 'تسجيل الخروج',
            onTap: () {
              _showLogoutDialog(context);
            },
            isLast: true,
          ),
        ],
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    // Capture the ProfileCubit from the current context before showing dialog
    final profileCubit = context.read<ProfileCubit>();
    debugPrint('🔄 ProfileView: Captured hoster ProfileCubit instance: ${profileCubit.hashCode}');

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                debugPrint('🔄 ProfileView: Hoster logout button pressed in dialog');
                debugPrint('🔄 ProfileView: Using captured hoster ProfileCubit instance: ${profileCubit.hashCode}');
                Navigator.of(dialogContext).pop();
                profileCubit.logout();
              },
              child: const Text('تسجيل الخروج'),
            ),
          ],
        );
      },
    );
  }
}

class _HosterMenuItem extends StatelessWidget {
  final IconData? icon;
  final String? image;
  final String title;
  final String? subtitle;
  final bool hasNew;
  final VoidCallback onTap;
  final bool isLast;

  const _HosterMenuItem({
    required this.title,
    this.subtitle,
    this.hasNew = false,
    required this.onTap,
    this.isLast = false,
    this.icon,
    this.image,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(
                  bottom: BorderSide(
                    color: context.secondaryTextColor.withValues(alpha: 0.1),
                    width: 0.5,
                  ),
                ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: context.primaryTextColor,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.font16Regular.copyWith(
                          color: context.primaryTextColor,
                        ),
                      ),
                      if (hasNew && subtitle != null) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: const Color(0xFFE91E63),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            subtitle!,
                            style: AppTextStyles.font10Regular.copyWith(
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: context.secondaryTextColor,
            ),
          ],
        ),
      ),
    );
  }
}

class _ProfileImage extends StatefulWidget {
  final String? image;

  const _ProfileImage({this.image});

  @override
  State<_ProfileImage> createState() => _ProfileImageState();
}

class _ProfileImageState extends State<_ProfileImage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: context.accentColor.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: CircleAvatar(
              radius: 50,
              backgroundColor: context.accentColor.withValues(alpha: 0.1),
              child: widget.image != null
                  ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: widget.image!,
                        width: 100,
                        height: 100,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade300,
                            shape: BoxShape.circle,
                          ),
                          child: const CircularProgressIndicator(),
                        ),
                        errorWidget: (context, url, error) => Icon(
                          Icons.person,
                          size: 50,
                          color: Colors.grey.shade400,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.person,
                      size: 50,
                      color: Colors.grey.shade400,
                    ),
            ),
          ),
        );
      },
    );
  }
}

class _ProfileInfo extends StatelessWidget {
  final UserEntity user;
  final String Function(int, S) getGenderString;

  const _ProfileInfo({
    required this.user,
    required this.getGenderString,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            S.of(context).personalInfo,
            style: AppTextStyles.font18Bold.copyWith(
              color: context.primaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          _InfoTile(
            icon: Icons.email_rounded,
            title: S.of(context).email,
            value: user.email,
            color: Colors.blue,
          ),
          _InfoTile(
            icon: Icons.phone_rounded,
            title: S.of(context).phone,
            value: user.phone,
            color: Colors.green,
          ),
          _InfoTile(
            icon: Icons.person_outline_rounded,
            title: S.of(context).gender,
            value: getGenderString(user.gender, S.of(context)),
            color: Colors.purple,
          ),
          _InfoTile(
            icon: Icons.cake_rounded,
            title: S.of(context).birthdate,
            value: user.birthdate,
            color: Colors.orange,
          ),
        ],
      ),
    );
  }
}

class _InfoTile extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? value;
  final Color color;

  const _InfoTile({
    required this.icon,
    required this.title,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.font12Regular.copyWith(
                    color: context.secondaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value ?? S.of(context).notSpecified,
                  style: AppTextStyles.font14SemiBold.copyWith(
                    color: context.primaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Client Profile Components
class _ClientProfileHeader extends StatelessWidget {
  final UserEntity user;

  const _ClientProfileHeader({required this.user});

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return EnhancedCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            // Profile Image with Heart Badge
            Stack(
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.grey.shade300,
                      width: 2,
                    ),
                  ),
                  child: ClipOval(
                    child: user.image.isNotEmpty && !user.isGuest
                        ? CachedNetworkImage(
                            imageUrl: user.image,
                            width: 120,
                            height: 120,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey.shade200,
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              width: 120,
                              height: 120,
                              color: Colors.grey.shade200,
                              child: Icon(
                                Icons.person,
                                size: 60,
                                color: Colors.grey.shade400,
                              ),
                            ),
                          )
                        : Container(
                            width: 120,
                            height: 120,
                            color: Colors.grey.shade200,
                            child: Icon(
                              Icons.person,
                              size: 60,
                              color: Colors.grey.shade400,
                            ),
                          ),
                  ),
                ),
                if (!user.isGuest)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: 36,
                      height: 36,
                      decoration: const BoxDecoration(
                        color: Colors.pink,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.favorite,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Name
            Text(
              user.isGuest ? s.guestUser : user.fullName,
              style: AppTextStyles.font20Bold.copyWith(
                color: context.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),

            // Location or Guest Message
            Text(
              user.isGuest ? s.loginForFullExperience : s.jeddahSaudiArabia,
              style: AppTextStyles.font14Regular.copyWith(
                color: context.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // Stats Row - only show for authenticated users
            if (!user.isGuest)
              BlocBuilder<ProfileCubit, ProfileState>(
                builder: (context, profileState) {
                  final accountStats = profileState is ProfileLoaded
                      ? profileState.accountStats
                      : null;

                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _ClientStatItem(
                        value: accountStats?.totalBookings.toString() ?? '0',
                        label: user.isHosterMode ? s.properties : 'رحلة سابقة',
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color:
                            context.secondaryTextColor.withValues(alpha: 0.2),
                      ),
                      _ClientStatItem(
                        value: accountStats?.averageRating.toStringAsFixed(1) ??
                            '0.0',
                        label: s.rating,
                      ),
                      Container(
                        width: 1,
                        height: 40,
                        color:
                            context.secondaryTextColor.withValues(alpha: 0.2),
                      ),
                      _ClientStatItem(
                        value: accountStats != null
                            ? DateTime.parse(accountStats.memberSince)
                                .year
                                .toString()
                            : DateTime.now().year.toString(),
                        label: 'مشترك منذ',
                      ),
                    ],
                  );
                },
              )
            else
              // Login button for guests
              Container(
                width: double.infinity,
                margin: const EdgeInsets.only(top: 10),
                child: ElevatedButton(
                  onPressed: () {
                    GoRouter.of(context).go(RoutesKeys.kLoginView);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.accentColor,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(
                    s.login,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _ClientStatItem extends StatelessWidget {
  final String value;
  final String label;

  const _ClientStatItem({
    required this.value,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: AppTextStyles.font18Bold.copyWith(
            color: context.primaryTextColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: AppTextStyles.font12Regular.copyWith(
            color: context.secondaryTextColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}

class _QuickActionsSection extends StatelessWidget {
  const _QuickActionsSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return Row(
      children: [
        Expanded(
          child: _QuickActionCard(
            icon: Icons.people_outline,
            title: s.knowledge,
            onTap: () {
              // Navigate to knowledge/friends section
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const FriendsScreen(),
                ),
              );
            },
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _QuickActionCard(
            icon: Icons.travel_explore,
            title: 'الرحلات السابقة',
            onTap: () {
              // Navigate to previous trips
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PreviousTripsScreen(),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return EnhancedCard(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.accentColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: context.accentColor,
              size: 32,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: AppTextStyles.font14SemiBold.copyWith(
              color: context.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

class _SettingsSection extends StatelessWidget {
  const _SettingsSection();

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    final cubit = context.read<ProfileCubit>();
    final isGuest = cubit.state is ProfileLoaded
        ? (cubit.state as ProfileLoaded).user.isGuest
        : true;

    return EnhancedCard(
      child: Column(
        children: [
          _ProfileMenuItem(
            icon: Icons.settings_outlined,
            title: s.accountSettingsTitle,
            onTap: () {
              if (isGuest) {
                _showLoginPrompt(context);
              } else {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const EnhancedAccountInfoScreen(),
                  ),
                );
              }
            },
          ),
          _ProfileMenuItem(
            icon: Icons.help_outline,
            title: s.requestHelp,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SupportCenterScreen(),
                ),
              );
            },
          ),
          _ProfileMenuItem(
            icon: Icons.security_outlined,
            title: s.privacy,
            onTap: () {
              if (isGuest) {
                _showLoginPrompt(context);
              } else {
                context.push('/privacy-settings');
              }
            },
          ),
          _ProfileMenuItem(
            icon: Icons.star_border,
            title: s.referHost,
            onTap: () {
              if (isGuest) {
                _showLoginPrompt(context);
              } else {
                context.push('/refer-host');
              }
            },
          ),
          _ProfileMenuItem(
            icon: Icons.description_outlined,
            title: s.legal,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const LegalInformationScreen(),
                ),
              );
            },
          ),
          if (!isGuest)
            _ProfileMenuItem(
              icon: Icons.logout,
              title: s.logout,
              onTap: () {
                _showLogoutDialog(context, s);
              },
              isDestructive: true,
            ),
        ],
      ),
    );
  }

  void _showLoginPrompt(BuildContext context) {
    final s = S.of(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(s.loginRequired),
        content: Text(s.loginRequiredMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(s.cancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              GoRouter.of(context).go(RoutesKeys.kLoginView);
            },
            child: Text(s.login),
          ),
        ],
      ),
    );
  }

  void _navigateToEditProfile(BuildContext context) async {
    final s = S.of(context);
    final cubit = context.read<ProfileCubit>();

    // Always ensure we have the latest user data
    if (cubit.state is! ProfileLoaded) {
      debugPrint(
          '🔄 ProfileCubit state is not ProfileLoaded, current state: ${cubit.state.runtimeType}');

      // Show loading message and try to reload
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(s.loadingData),
          duration: const Duration(seconds: 2),
        ),
      );

      cubit.loadUserInfo();
      return;
    }

    final user = (cubit.state as ProfileLoaded).user;
    final result = await context.push('/edit-profile', extra: {'user': user});
    if (result != null && result is UserEntity) {
      cubit.loadUserInfo(); // Refresh user data
    }
  }

  void _showProfileDetails(BuildContext context) {
    final cubit = context.read<ProfileCubit>();
    if (cubit.state is ProfileLoaded) {
      final user = (cubit.state as ProfileLoaded).user;
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _ProfileImage(image: user.image),
                      const SizedBox(height: 16),
                      Text(
                        user.fullName,
                        style: AppTextStyles.font24Bold.copyWith(
                          color: context.primaryTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        user.bio,
                        style: AppTextStyles.font16Regular.copyWith(
                          color: context.secondaryTextColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      _ProfileInfo(
                          user: user,
                          getGenderString: (gender, s) {
                            switch (gender) {
                              case 1:
                                return s.male;
                              case 2:
                                return s.female;
                              default:
                                return s.notSpecified;
                            }
                          }),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  void _showLogoutDialog(BuildContext context, S s) {
    // Capture the ProfileCubit from the current context before showing dialog
    final profileCubit = context.read<ProfileCubit>();
    debugPrint('🔄 ProfileView: Captured ProfileCubit instance: ${profileCubit.hashCode}');

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: Text(s.logout),
        content: Text(s.logoutConfirmation),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext),
            child: Text(s.cancel),
          ),
          TextButton(
            onPressed: () {
              debugPrint('🔄 ProfileView: Logout button pressed in dialog');
              debugPrint('🔄 ProfileView: Using captured ProfileCubit instance: ${profileCubit.hashCode}');
              Navigator.pop(dialogContext);
              // Perform logout using the captured ProfileCubit
              profileCubit.logout();
            },
            child: Text(s.logout),
          ),
        ],
      ),
    );
  }
}

class _ProfileMenuItem extends StatefulWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;
  final bool showArrow;
  final bool isDestructive;

  const _ProfileMenuItem({
    required this.icon,
    required this.title,
    required this.onTap,
    this.subtitle,
    this.showArrow = true,
    this.isDestructive = false,
  });

  @override
  State<_ProfileMenuItem> createState() => _ProfileMenuItemState();
}

class _ProfileMenuItemState extends State<_ProfileMenuItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _animationController.reverse();
    widget.onTap();
  }

  void _onTapCancel() {
    setState(() => _isPressed = false);
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color: _isPressed
                    ? context.accentColor.withValues(alpha: 0.1)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
                child: Row(
                  children: [
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: widget.isDestructive
                            ? Colors.red.withValues(alpha: 0.1)
                            : context.accentColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        widget.icon,
                        color: widget.isDestructive
                            ? Colors.red
                            : context.accentColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: AppTextStyles.font16Regular.copyWith(
                              color: widget.isDestructive
                                  ? Colors.red
                                  : context.primaryTextColor,
                            ),
                          ),
                          if (widget.subtitle != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              widget.subtitle!,
                              style: AppTextStyles.font12Regular.copyWith(
                                color: context.secondaryTextColor,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    if (widget.showArrow)
                      AnimatedRotation(
                        turns: _isPressed ? 0.05 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.arrow_forward_ios,
                          color: context.secondaryTextColor,
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Shimmer Loading Widget
class _ProfileLoadingShimmer extends StatefulWidget {
  const _ProfileLoadingShimmer();

  @override
  State<_ProfileLoadingShimmer> createState() => _ProfileLoadingShimmerState();
}

class _ProfileLoadingShimmerState extends State<_ProfileLoadingShimmer>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    BorderRadius? borderRadius,
    BoxShape shape = BoxShape.rectangle,
  }) {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            shape: shape,
            borderRadius: shape == BoxShape.rectangle ? borderRadius : null,
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [
                Colors.grey.shade300,
                Colors.grey.shade100,
                Colors.grey.shade300,
              ],
              stops: [
                _shimmerAnimation.value - 0.3,
                _shimmerAnimation.value,
                _shimmerAnimation.value + 0.3,
              ].map((stop) => stop.clamp(0.0, 1.0)).toList(),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Profile Header Shimmer
          EnhancedCard(
            child: Column(
              children: [
                // Profile Image Shimmer
                _buildShimmerContainer(
                  width: 100,
                  height: 100,
                  shape: BoxShape.circle,
                ),
                const SizedBox(height: 20),

                // Name Shimmer
                _buildShimmerContainer(
                  width: 200,
                  height: 24,
                  borderRadius: BorderRadius.circular(12),
                ),
                const SizedBox(height: 12),

                // Bio Shimmer
                _buildShimmerContainer(
                  width: 150,
                  height: 16,
                  borderRadius: BorderRadius.circular(8),
                ),
                const SizedBox(height: 20),

                // Stats Row Shimmer
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    3,
                    (index) => Column(
                      children: [
                        _buildShimmerContainer(
                          width: 48,
                          height: 48,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        const SizedBox(height: 8),
                        _buildShimmerContainer(
                          width: 40,
                          height: 16,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        const SizedBox(height: 4),
                        _buildShimmerContainer(
                          width: 60,
                          height: 12,
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Quick Actions Shimmer
          EnhancedCard(
            child: Column(
              children: [
                Container(
                  width: 120,
                  height: 20,
                  decoration: BoxDecoration(
                    color: Colors.grey.shade300,
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: List.generate(
                    2,
                    (index) => Expanded(
                      child: Container(
                        margin: EdgeInsets.only(right: index == 0 ? 8 : 0),
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),

          // Menu Items Shimmer
          ...List.generate(
            4,
            (index) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: EnhancedCard(
                child: Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            width: double.infinity,
                            height: 16,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: 200,
                            height: 12,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade300,
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Mode Switch Floating Action Button
class _ModeSwitchButton extends StatefulWidget {
  final UserEntity user;

  const _ModeSwitchButton({required this.user});

  @override
  State<_ModeSwitchButton> createState() => _ModeSwitchButtonState();
}

class _ModeSwitchButtonState extends State<_ModeSwitchButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onPressed() async {
    // Check if trying to enable host mode and profile is incomplete
    if (!widget.user.isHosterMode &&
        !AuthUtils.isProfileCompleteForHostMode()) {
      _showIncompleteProfileDialog();
      return;
    }

    // Show fullscreen flip animation overlay
    _showModeTransitionOverlay();

    // Animate button press
    await _animationController.forward();
    await _animationController.reverse();

    // Toggle hoster mode - switch to opposite of current mode
    if (mounted) {
      context.read<ProfileCubit>().toggleHosterMode(!widget.user.isHosterMode);
    }
  }

  void _showIncompleteProfileDialog() {
    final missingFields = AuthUtils.getMissingProfileFieldsForHostMode();
    final Map<String, String> fieldNames = {
      'full_name': 'الاسم الكامل',
      'email': 'البريد الإلكتروني',
      'phone': 'رقم الهاتف',
      'bio': 'النبذة الشخصية',
      'birthdate': 'تاريخ الميلاد',
      'gender': 'الجنس',
      'phone_verification': 'تأكيد رقم الهاتف',
      'login_required': 'تسجيل الدخول مطلوب',
    };

    final missingFieldsText =
        missingFields.map((field) => fieldNames[field] ?? field).join('، ');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إكمال الملف الشخصي مطلوب'),
        content: Text(
          'يجب إكمال الملف الشخصي أولاً لتفعيل وضع المضيف.\n\nالحقول المطلوبة:\n$missingFieldsText\n\nهل تريد الانتقال لتحديث الملف الشخصي الآن؟',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToEditProfile();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: context.accentColor,
              foregroundColor: Colors.black,
            ),
            child: const Text('تحديث الملف الشخصي'),
          ),
        ],
      ),
    );
  }

  void _navigateToEditProfile() async {
    // Navigate to edit profile screen with user data
    final result =
        await context.push('/edit-profile', extra: {'user': widget.user});

    // Check if profile is now complete after returning from edit screen
    if (result != null || AuthUtils.isProfileCompleteForHostMode()) {
      // Profile is complete, now switch to host mode
      if (mounted) {
        // Show the mode transition overlay
        _showModeTransitionOverlay();

        // Animate button press
        await _animationController.forward();
        await _animationController.reverse();

        // Toggle to host mode
        if (mounted) {
          context.read<ProfileCubit>().toggleHosterMode(true);
        }
      }
    }
  }

  void _showModeTransitionOverlay() {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (context) => _ModeTransitionOverlay(
        fromHostMode: widget.user.isHosterMode,
        toHostMode: !widget.user.isHosterMode,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final s = S.of(context);
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform.rotate(
            angle: _rotationAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: context.accentColor.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                  ),
                ],
              ),
              child: FloatingActionButton.extended(
                onPressed: _onPressed,
                backgroundColor: context.accentColor,
                foregroundColor: Colors.black,
                elevation: 0,
                highlightElevation: 0,
                icon: Icon(
                  widget.user.isHosterMode
                      ? Icons.travel_explore_rounded
                      : Icons.home_work_rounded,
                  size: 24,
                ),
                label: Text(
                  widget.user.isHosterMode ? s.switchToTravel : s.hosting,
                  style: AppTextStyles.font14Bold.copyWith(
                    color: Colors.black,
                  ),
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// Fullscreen Mode Transition Overlay
class _ModeTransitionOverlay extends StatefulWidget {
  final bool fromHostMode;
  final bool toHostMode;

  const _ModeTransitionOverlay({
    required this.fromHostMode,
    required this.toHostMode,
  });

  @override
  State<_ModeTransitionOverlay> createState() => _ModeTransitionOverlayState();
}

class _ModeTransitionOverlayState extends State<_ModeTransitionOverlay>
    with TickerProviderStateMixin {
  late AnimationController _flipController;
  late AnimationController _scaleController;
  late Animation<double> _flipAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Animation for the flip effect
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Animation for the scale effect
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _startAnimation();
  }

  void _startAnimation() async {
    // Start scale animation first
    _scaleController.forward();

    // Wait a bit then start flip animation
    await Future.delayed(const Duration(milliseconds: 200));
    _flipController.forward();

    // Auto-close after animation completes
    await Future.delayed(const Duration(milliseconds: 1500));
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _flipController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  Widget _buildModeImage(bool isHostMode) {
    // Try to load custom images first, fall back to icons if they fail
    return Image.asset(
      isHostMode
          ? 'assets/images/host_mode.png'
          : 'assets/images/client_mode.png',
      width: 200,
      height: 200,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // Fallback to large icons with background
        return Container(
          width: 200,
          height: 200,
          decoration: BoxDecoration(
            color: context.accentColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(100),
          ),
          child: Icon(
            isHostMode ? Icons.home_work_rounded : Icons.travel_explore_rounded,
            size: 100,
            color: context.accentColor,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([_flipController, _scaleController]),
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: 250,
                height: 250,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(125),
                  boxShadow: [
                    BoxShadow(
                      color: context.accentColor.withValues(alpha: 0.4),
                      blurRadius: 30,
                      spreadRadius: 10,
                    ),
                  ],
                ),
                child: AnimatedBuilder(
                  animation: _flipAnimation,
                  builder: (context, child) {
                    final isShowingFront = _flipAnimation.value < 0.5;
                    return Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001)
                        ..rotateY(_flipAnimation.value * 3.14159),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(125),
                          color: Colors.white,
                          border: Border.all(
                            color: context.accentColor,
                            width: 4,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(121),
                          child: isShowingFront
                              ? _buildModeImage(widget.fromHostMode)
                              : Transform(
                                  alignment: Alignment.center,
                                  transform: Matrix4.identity()
                                    ..rotateY(3.14159),
                                  child: _buildModeImage(widget.toHostMode),
                                ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
