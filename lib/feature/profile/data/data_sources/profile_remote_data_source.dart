import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';

abstract class ProfileRemoteDataSource {
  Future<UserEntity> getUserInfo();
  Future<AccountInfo> getAccountInfo();
  Future<UserEntity> editProfile({
    String? fullName,
    String? email,
    String? phone,
    String? bio,
    String? birthdate,
    int? gender,
    String? profilePicturePath,
  });
  Future<void> logout();
  Future<UserEntity> toggleHosterMode({required bool isHosterMode});
  Future<void> deleteAccount();
  Future<List<UserEntity>> getFollowList();
  Future<void> followUser({required int userId});
  Future<void> unfollowUser({required int userId});
}

class ProfileRemoteDataSourceImpl implements ProfileRemoteDataSource {
  final DioConsumer apiConsumer;

  ProfileRemoteDataSourceImpl({required this.apiConsumer});

  @override
  Future<UserEntity> getUserInfo() async {
    var data = await apiConsumer.get(EndPoints.clientInfo);
    UserEntity user = UserModel.fromJson(data['data']);
    return user;
  }

  @override
  Future<AccountInfo> getAccountInfo() async {
    var data = await apiConsumer.get(EndPoints.accountInfo);
    AccountInfo accountInfo = AccountInfo.fromJson(data['data']);
    return accountInfo;
  }

  @override
  Future<UserEntity> editProfile({
    String? fullName,
    String? email,
    String? phone,
    String? bio,
    String? birthdate,
    int? gender,
    String? profilePicturePath,
  }) async {
    Map<String, dynamic> requestData = {};
    
    if (fullName != null) requestData['full_name'] = fullName;
    if (email != null) requestData['email'] = email;
    if (phone != null) requestData['phone'] = phone;
    if (bio != null) requestData['bio'] = bio;
    if (birthdate != null) requestData['birthdate'] = birthdate;
    if (gender != null) requestData['gender'] = gender;

    // Handle profile picture upload
    if (profilePicturePath != null) {
      requestData['profile_picture'] = await MultipartFile.fromFile(
        profilePicturePath,
        filename: profilePicturePath.split('/').last,
      );
    }

    FormData formData = FormData.fromMap(requestData);

    var data = await apiConsumer.post(
      EndPoints.editProfile,
      data: formData,
      isFormData: true,
    );

    UserEntity user = UserModel.fromJson(data['data']);
    return user;
  }

  @override
  Future<void> logout() async {
    try {
      debugPrint('🔄 ProfileRemoteDataSource: Calling logout API');
      final response = await apiConsumer.post(EndPoints.logout);
      debugPrint('✅ ProfileRemoteDataSource: Logout API response: $response');
    } catch (e) {
      debugPrint('❌ ProfileRemoteDataSource: Logout API error: $e');

      // If the error is 401 Unauthorized, it means the token is already invalid
      // In this case, we should still proceed with local logout
      if (e is DioException && e.response?.statusCode == 401) {
        debugPrint('🔄 ProfileRemoteDataSource: Token already invalid (401), proceeding with local logout');
        return; // Don't rethrow, allow local logout to proceed
      }

      // For other errors, still allow local logout to proceed
      debugPrint('🔄 ProfileRemoteDataSource: API logout failed, but proceeding with local logout');
      return; // Don't rethrow, allow local logout to proceed
    }
  }

  @override
  Future<UserEntity> toggleHosterMode({required bool isHosterMode}) async {
    var data = await apiConsumer.post(
      EndPoints.toggleHosterMode,
      data: {'is_hoster_mode': isHosterMode},
    );

    UserEntity user = UserModel.fromJson(data['data']);
    return user;
  }

  @override
  Future<void> deleteAccount() async {
    await apiConsumer.delete(EndPoints.deleteAccount);
  }

  @override
  Future<List<UserEntity>> getFollowList() async {
    var data = await apiConsumer.get(EndPoints.followList);

    List<UserEntity> followList = [];
    if (data['data'] != null) {
      for (var item in data['data']) {
        followList.add(UserModel.fromJson(item));
      }
    }

    return followList;
  }

  @override
  Future<void> followUser({required int userId}) async {
    await apiConsumer.post(
      EndPoints.follow,
      data: {'user_id': userId},
    );
  }

  @override
  Future<void> unfollowUser({required int userId}) async {
    await apiConsumer.delete(
      EndPoints.unfollow,
      data: {'user_id': userId},
    );
  }
}
