import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/errors/failure.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/profile/data/data_sources/profile_remote_data_source.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';
import 'package:gather_point/feature/profile/domain/repositories/profile_repository.dart';

class ProfileRepositoryImpl implements ProfileRepository {
  final ProfileRemoteDataSource remoteDataSource;

  ProfileRepositoryImpl({required this.remoteDataSource});

  @override
  Future<Either<Failure, UserEntity>> getUserInfo() async {
    try {
      final user = await remoteDataSource.getUserInfo();
      return Right(user);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, AccountInfo>> getAccountInfo() async {
    try {
      final accountInfo = await remoteDataSource.getAccountInfo();
      return Right(accountInfo);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> editProfile({
    String? fullName,
    String? email,
    String? phone,
    String? bio,
    String? birthdate,
    int? gender,
    String? profilePicturePath,
  }) async {
    try {
      final user = await remoteDataSource.editProfile(
        fullName: fullName,
        email: email,
        phone: phone,
        bio: bio,
        birthdate: birthdate,
        gender: gender,
        profilePicturePath: profilePicturePath,
      );
      return Right(user);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> logout() async {
    try {
      debugPrint('🔄 ProfileRepository: Calling remote data source logout');
      await remoteDataSource.logout();
      debugPrint('✅ ProfileRepository: Remote logout completed successfully');
      return const Right(null);
    } catch (e) {
      debugPrint('❌ ProfileRepository: Remote logout failed with error: $e');
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, UserEntity>> toggleHosterMode({
    required bool isHosterMode,
  }) async {
    try {
      final user = await remoteDataSource.toggleHosterMode(
        isHosterMode: isHosterMode,
      );
      return Right(user);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteAccount() async {
    try {
      await remoteDataSource.deleteAccount();
      return const Right(null);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<UserEntity>>> getFollowList() async {
    try {
      final followList = await remoteDataSource.getFollowList();
      return Right(followList);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> followUser({required int userId}) async {
    try {
      await remoteDataSource.followUser(userId: userId);
      return const Right(null);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> unfollowUser({required int userId}) async {
    try {
      await remoteDataSource.unfollowUser(userId: userId);
      return const Right(null);
    } catch (e) {
      return Left(Failure(errMessage: e.toString()));
    }
  }
}
