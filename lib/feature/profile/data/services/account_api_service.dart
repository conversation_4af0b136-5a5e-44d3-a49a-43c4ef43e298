import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gather_point/core/databases/api/dio_consumer.dart';
import 'package:gather_point/core/databases/api/end_points.dart';
import 'package:gather_point/feature/profile/data/models/account_models.dart';
import 'package:gather_point/feature/auth/Domain/Entities/user_entity.dart';
import 'package:gather_point/feature/auth/Data/Models/user_model/user_model.dart';

class AccountApiService {
  final DioConsumer _dioConsumer;

  AccountApiService(this._dioConsumer);
  
  /// Get account information
  Future<AccountInfo> getAccountInfo() async {
    try {
      final response = await _dioConsumer.get('/api/account/info');

      if (response['success'] == true) {
        return AccountInfo.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب معلومات الحساب');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Update account information
  Future<UserEntity> updateAccountInfo({
    String? fullName,
    String? email,
    String? phone,
    String? birthdate,
    int? gender,
    String? bio,
    File? profilePicture,
  }) async {
    try {
      FormData formData = FormData();

      // Add fields
      if (fullName != null) formData.fields.add(MapEntry('full_name', fullName));
      if (email != null) formData.fields.add(MapEntry('email', email));
      if (phone != null) formData.fields.add(MapEntry('phone', phone));
      if (birthdate != null) formData.fields.add(MapEntry('birthdate', birthdate));
      if (gender != null) formData.fields.add(MapEntry('gender', gender.toString()));
      if (bio != null) formData.fields.add(MapEntry('bio', bio));

      // Add profile picture if provided
      if (profilePicture != null) {
        formData.files.add(
          MapEntry(
            'profile_picture',
            await MultipartFile.fromFile(
              profilePicture.path,
              filename: profilePicture.path.split('/').last,
            ),
          ),
        );
      }

      final response = await _dioConsumer.put(
        EndPoints.editProfile,
        data: formData,
      );

      if (response['success'] == true) {
        return UserModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في تحديث معلومات الحساب');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Change password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final requestData = {
        'current_password': currentPassword,
        'new_password': newPassword,
        'new_password_confirmation': confirmPassword,
      };

      final response = await _dioConsumer.post('/api/account/change-password', data: requestData);

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في تغيير كلمة المرور');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Get security settings
  Future<SecuritySettings> getSecuritySettings() async {
    try {
      final response = await _dioConsumer.get('/api/account/security');

      if (response['success'] == true) {
        return SecuritySettings.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'فشل في جلب إعدادات الأمان');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Deactivate account
  Future<void> deactivateAccount({
    required String reason,
    required String password,
  }) async {
    try {
      final requestData = {
        'reason': reason,
        'password': password,
      };

      final response = await _dioConsumer.post('/api/account/deactivate', data: requestData);

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'فشل في إلغاء تفعيل الحساب');
      }
    } catch (e) {
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }

  /// Export account data
  Future<AccountExportData> exportAccountData() async {
    try {
      debugPrint('🔄 AccountApiService: Calling export data API');
      final response = await _dioConsumer.get('/api/account/export-data');
      debugPrint('✅ AccountApiService: Export data API response: $response');

      if (response['success'] == true) {
        debugPrint('✅ AccountApiService: Parsing export data from JSON');
        return AccountExportData.fromJson(response['data']);
      } else {
        debugPrint('❌ AccountApiService: Export data API returned error: ${response['message']}');
        throw Exception(response['message'] ?? 'فشل في تصدير بيانات الحساب');
      }
    } catch (e) {
      debugPrint('❌ AccountApiService: Export data error: $e');
      throw Exception('خطأ في الاتصال: ${e.toString()}');
    }
  }
}
